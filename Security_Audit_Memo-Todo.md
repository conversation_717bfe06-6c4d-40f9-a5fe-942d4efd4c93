# Gemini 安全性審查備忘錄 - 待辦項目

這份文件記錄了由 Gemini 根據 OWASP Top 10 對 Intra2025 專案進行的程式碼安全性審查所發現的潛在風險與改善建議中，尚未完成的項目。

---

## **【中風險】**

### **A04:2021 – Insecure Design (不安全的設計)**

*（Problem #4.1 已完成修復，已移至 Security_Audit_Memo-Finish.md）*

-   **Problem #4.2:** [ ] **【中風險】** **缺乏稽核日誌**:
    -   **問題**: 敏感操作（查看、編輯、刪除兒童案件）缺乏完整的稽核日誌記錄。
    -   **位置**: 多個 YCRS 相關頁面
    -   **影響**: 無法追蹤敏感資料的存取和修改歷史
    -   **建議**: 為所有敏感操作添加詳細的稽核日誌記錄

-   **Problem #4.3:** [ ] **【中風險】** **跨站請求偽造漏洞 (CSRF)**:
    -   **問題**: 執行狀態更改操作的表單可能缺少 CSRF 令牌，導致攻擊者可以誘騙使用者執行非預期的操作。
    -   **建議**: 對所有執行敏感操作的 POST 請求實作 CSRF 保護，例如使用 Blazor 內建的 CSRF 令牌。

-   **Problem #4.4:** [ ] **【中風險】** **QR Code 安全性**:
    -   **問題**: `Services/ComRemitQRCodeService.cs` 在生成或掃描 QR Code 時，如果未對內容進行適當驗證，可能引入漏洞。
    -   **建議**: 確保 QR Code 內容的生成和解析是安全的，並對其進行驗證，以防止惡意資料注入。

*（Problem #4.5 已完成修復，已移至 Security_Audit_Memo-Finish.md）*

---

### **A05:2021 – Security Misconfiguration (安全設定錯誤)**

-   **Problem #5.1:** [ ] **【中風險】** **錯誤處理不當 - 資訊洩露**:
    -   **問題**: 錯誤處理直接向使用者顯示異常訊息，可能洩露系統內部資訊。
    -   **位置**:
        - `ChildCaseList.razor`: `ErrorMessage = $"刪除過程中發生錯誤：{ex.Message}";`
        - `RemitedList.razor`: `await JSRuntime.InvokeVoidAsync("alert", $"匯出失敗: {ex.Message}");`
    -   **影響**: 可能洩露敏感系統資訊給攻擊者
    -   **建議**: 實作通用錯誤訊息，詳細錯誤資訊僅記錄到日誌中

-   **Problem #5.2:** [ ] **【高風險】** **硬編碼憑證**:
    -   **問題**: 資料庫連線字串和 API 金鑰直接硬編碼在 `appsettings.json` 和 `appsettings.Development.json` 中。
    -   **建議**: 將敏感憑證和金鑰移至安全的配置管理系統（如 Azure Key Vault、環境變數或使用者機密）。

-   **Problem #5.3:** [ ] **【中風險】** **過度的資料暴露**:
    -   **問題**: 非管理員使用者可能看到不應該存取的案件資料，缺乏細粒度的權限控制。
    -   **位置**: 所有案件清單頁面
    -   **影響**: 可能洩露不應該被存取的敏感資料
    -   **建議**: 根據使用者權限實作更細粒度的資料過濾

-   **Problem #5.4:** [ ] **【低風險】** **輸入驗證不足**:
    -   **問題**: 搜尋輸入框缺乏長度限制和格式驗證。
    -   **位置**: `ChildCaseList.razor`, `CareCaseList.razor` 等搜尋功能
    -   **影響**: 可能導致效能問題或異常行為
    -   **建議**: 添加輸入長度限制和格式驗證

---

### **A06:2021 – Vulnerable and Outdated Components (有漏洞和過時的組件)**

-   **Problem #6.1:** [ ] **【高風險】** **使用過時的函式庫**:
    -   **問題**: 專案使用多個已知漏洞的過時 NuGet 套件（例如 `Newtonsoft.Json`）。
    -   **建議**: 定期更新所有第三方函式庫和框架到最新穩定版本，並監控安全公告。

---
### **A08:2021 – Software and Data Integrity Fail failures (軟體與資料完整性故障)**

-   **Problem #8:** [ ] **檔案上傳/下載安全性**: 檢查處理檔案上傳 (`/uploads`) 和下載 (`/exports`) 的程式碼，確保有對檔案類型、大小、內容進行驗證，並在下載時檢查使用者權限，以防止路徑遍歷等攻擊。

-   **Problem #8.4:** [V] **【高風險】** **無限制檔案上傳漏洞**:
    -   **問題**: 在 `wwwroot/uploads/Reports/` 等目錄中，可能存在未經適當驗證檔案類型或內容的無限制檔案上傳，允許攻擊者上傳惡意檔案。
    -   **建議**: 實作嚴格的檔案上傳驗證，包括白名單檔案類型、限制檔案大小、掃描惡意內容，並將上傳檔案儲存在非 Web 可存取的目錄中。
    -   **備註**: 已完成修復，已移至 Security_Audit_Memo-Finish.md

---

### **A09:2021 – Security Logging and Monitoring Failures (安全日誌記錄與監控失敗)**

-   **Problem #9.1:** [ ] **【中風險】** **日誌記錄和監控不足**:
    -   **問題**: 安全相關事件（例如登入失敗、授權失敗、異常活動）的日誌記錄不足，或缺乏有效的監控機制。
    -   **建議**: 實作全面的安全日誌記錄，包括所有安全相關事件，並建立監控和警報機制。

---
### **需要進一步分析的項目**

-   **Problem #8:** [ ] **檔案上傳/下載安全性**: 檢查處理檔案上傳 (`/uploads`) 和下載 (`/exports`) 的程式碼，確保有對檔案類型、大小、內容進行驗證，並在下載時檢查使用者權限，以防止路徑遍歷等攻擊。

