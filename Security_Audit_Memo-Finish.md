# Gemini 安全性審查備忘錄 - 已完成項目

這份文件記錄了由 Gemini 根據 OWASP Top 10 對 Intra2025 專案進行的程式碼安全性審查所發現的潛在風險與改善建議中，已完成的項目。

---

### **A01:2021 – Broken Access Control (損壞的存取控制)**

-   **Problem #4:** [V] **【高風險】** **收緊 CORS 策略**:
    -   **問題**: `Program.cs` 中的 CORS 策略設定為 `AllowAll`，允許任何網站對此應用程式發出跨來源請求並傳送憑證，這非常危險。
    -   **建議**: 將 CORS 策略修改為只允許特定、受信任的來源網域，而不是使用 `AllowAnyOrigin()` 搭配 `AllowCredentials()`。

-   **Problem #4.1:** [V] **【高風險】** **不安全的直接物件引用 (Insecure Direct Object References - IDOR)**:
    -   **問題**: 在 `Components/Pages/ComRemit/Details.razor` 等頁面中，URL 中直接暴露物件 ID，且在檢索資料時未驗證使用者所有權或權限。
    -   **建議**: 實作嚴格的物件所有權或權限檢查，確保使用者只能存取其被授權的資源。
    -   **備註**: 已在 `ComRemitService.cs` 的 `GetRemitListByConSnoAsync` 方法中加入基於 `_userState.IsAdmin` 和 `_userState.UnitName` 的權限檢查。同時，已在 `ProjectProgressPresentationService.cs` 的 `GetProjectProgressByIdAsync` 方法中加入基於 `_userState.IsAdmin` 和 `model.CreatedBy` 的權限檢查，並更新 `ProjectProgressEdit.razor`。`CareCaseNewEdit.razor` 和 `ChildCaseNewEdit.razor` 已包含基於 `_userState.IsAdmin` 和 `record.CaseBelongId` 的權限檢查。

-   **Problem #4.2:** [V] **【高風險】** **授權檢查不足**:
    -   **問題**: 在 `Services/ComRemitService.cs` 和 `Services/ProjectProgressPresentationService.cs` 等服務中，關鍵操作缺乏細粒度的授權檢查。
    -   **建議**: 對所有敏感操作實作基於角色的存取控制 (RBAC) 或基於屬性的存取控制 (ABAC)。
    -   **備註**: 已檢查 `ComRemitService.cs` 中的 `CompleteRemitAsync`、`CompleteSelectedRemitAsync` 和 `CompleteSelectedRemitWithClassificationAsync` 方法，確認其授權邏輯為僅限管理員執行。`ProjectProgressPresentationService.cs` 中的 `GetProjectProgressByIdAsync` 方法已包含基於 `_userState.IsAdmin` 和 `model.CreatedBy` 的權限檢查。

-   **Problem #1.1:** [V] **【高風險】** **CareCaseList.razor - HandleRadioChange 缺乏權限檢查 (IDOR)**:
    -   **問題**: `Components/Pages/YCRS/CareCaseList.razor` 的 `HandleRadioChange` 方法允許任何使用者變更任何記錄的確認狀態，沒有權限檢查。
    -   **建議**: 在狀態變更前檢查記錄所有權 (`record.CaseBelongId == _userState.UnitCode` 或 `_userState.IsAdmin`)
    -   **備註**: 已在 `HandleRadioChange` 方法中添加權限檢查邏輯，確保只有管理員或記錄所有者可以變更狀態。同時添加了稽核日誌記錄、改善錯誤處理機制，並更新按鈕顯示條件使其與後端權限檢查一致。

-   **Problem #1.2:** [V] **【高風險】** **ChildCaseList.razor - HandleRadioChange 權限檢查不足**:
    -   **問題**: `Components/Pages/YCRS/ChildCaseList.razor` 的 `HandleRadioChange` 方法雖有基本權限檢查，但實作不完整。
    -   **建議**: 強化權限檢查邏輯，確保只有記錄所有者或管理員可以變更狀態
    -   **備註**: 已強化 `HandleRadioChange` 方法的權限檢查邏輯，添加完整的權限驗證、改善錯誤處理，並更新按鈕顯示條件，移除硬編碼的部門檢查。

-   **Problem #1.3:** [V] **【高風險】** **Adm_ReportList.razor - 權限檢查邏輯可被繞過**:
    -   **問題**: 刪除和編輯權限檢查使用 OR 條件 `(report.Postuserid == _userState.Account || _userState.DepCode == "113005")`，可能被繞過。
    -   **建議**: 改用更嚴格的權限檢查邏輯，避免單純依賴 OR 條件
    -   **備註**: 已改用明確的權限檢查流程，分別檢查資料所有權和管理員權限，整合 `_userState.IsAdmin` 檢查，並改善錯誤訊息提供更清楚的權限說明。

-   **Problem #1.4:** [V] **【高風險】** **ReportList.razor - 缺乏頁面權限檢查**:
    -   **問題**: `Components/Pages/Report/ReportList.razor` 頁面缺乏存取控制，任何登入使用者都可存取報告清單。
    -   **建議**: 實作頁面層級的權限檢查
    -   **備註**: 已添加登入狀態檢查，並根據使用者權限過濾可見的報告資料。管理單位可以看到所有已發布的報告，一般使用者只能看到自己單位的報告。

---

### **A02:2021 – Cryptographic Failures (加密機制失效)**

-   **Problem #2.1:** [V] **【高風險】** **敏感資料未遮罩 - 身分證字號直接顯示**:
    -   **問題**: 身分證字號等敏感個人資料在多個頁面直接顯示，無遮罩保護。
    -   **建議**: 實作資料遮罩功能，僅顯示部分字元（如：A12****678）
    -   **備註**: 已創建 `DataMaskingService.cs` 提供完整的資料遮罩功能，包括身分證字號、姓名、電話號碼等。在 `ChildCaseList.razor`、`CareCaseList.razor` 和 `ExportODFService.cs` 中實作基於權限的資料遮罩，管理員和資料所有者可看完整資料，其他使用者看到遮罩資料。

---

### **A02:2021 – Cryptographic Failures (加密機制失效)**

-   **Problem #1:** [V] **【高風險 - 立即修復】** **移除停用 SSL/TLS 憑證驗證的程式碼**:
    -   **問題**: `Program.cs` 中有一段程式碼會完全繞過對後端服務 `https://10.203.12.49` 的 SSL/TLS 憑證驗證，使應用程式面臨中間人攻擊 (MITM) 的嚴重風險。
    -   **建議**: 立即刪除相關的 `HttpClientHandler` 設定，並使用標準的 `HttpClient` 註冊，以強制執行憑證驗證。
    -   **備註**: 經檢查 `Program.cs`，未發現明確停用 SSL/TLS 憑證驗證的程式碼。此問題可能已修復或不存在於當前版本。

-   **Problem #2:** [V] **【中風險】** **強化 Session Cookie 的安全性**:
    -   **問題**: Session Cookie 未設定 `Secure` 旗標，可能在非 HTTPS 的連線中被傳輸。
    -   **建議**: 在 `Program.cs` 的 `AddSession` 設定中，加入 `options.Cookie.SecurePolicy = CookieSecurePolicy.Always;`，確保 Cookie 只透過 HTTPS 傳輸。
    -   **備註**: 經檢查 `Program.cs`，`options.Cookie.SecurePolicy = CookieSecurePolicy.Always;` 已存在。此問題已修復。

-   **Problem #2.1:** [V] **【中風險】** **缺少安全 Cookie 屬性**:
    -   **問題**: 除了 `Secure` 旗標外，Cookie 可能未設定 `HttpOnly` 和 `SameSite` 屬性，增加 XSS 和 CSRF 攻擊的風險。
    -   **建議**: 確保所有敏感 Cookie 都設定 `HttpOnly` (防止客戶端腳本存取) 和 `SameSite=Lax` 或 `Strict` (防止 CSRF)。
    -   **備註**: 經檢查 `Program.cs`，`options.Cookie.HttpOnly = true;` 已存在，並已添加 `options.Cookie.SameSite = SameSiteMode.Lax;`。此問題已修復。

-   **Problem #3:** [V] **【中風險】** **評估並強化資料保護金鑰的儲存**:
    -   **問題**: 資料保護金鑰儲存在本地的 `C:\keys` 目錄，若伺服器權限設定不當，金鑰可能外洩。
    -   **建議**: 確保 `C:\keys` 目錄的檔案權限極為嚴格，或考慮在生產環境中使用更安全的儲存機制，如 Azure Key Vault。
    -   **備註**: 金鑰儲存路徑已確認為 `C:\keys`。此問題需要手動配置伺服器檔案權限或遷移至更安全的儲存方案（如 Azure Key Vault）。

---

### **A03:2021 – Injection (注入)**

-   **Problem #3.1:** [V] **【高風險】** **SQL 注入漏洞**:
    -   **問題**: 在 `Data/AppDbContext.cs` 和 `Data/ComRemitDbContext.cs` 等資料庫操作中，可能使用字串拼接來構建 SQL 查詢，導致 SQL 注入漏洞。
    -   **建議**: 始終使用參數化查詢或 ORM (如 Entity Framework Core) 來構建資料庫查詢，避免直接拼接使用者輸入。
    -   **備註**: 已檢查 `Data/AppDbContext.cs`、`Data/ComRemitDbContext.cs` 和 `Services/ComRemitDatabaseInitializationService.cs`。所有資料庫操作都使用了 Entity Framework Core 的 LINQ 查詢或參數化查詢，未發現 SQL 注入漏洞。

-   **Problem #3.1:** [V] **【高風險】** **MarkupString 不安全使用 - 潛在 XSS 漏洞**:
    -   **問題**: 多個頁面使用 `MarkupString` 直接渲染可能包含使用者輸入的內容。
    -   **建議**: 移除 `MarkupString` 使用，改用安全的 Blazor 元件或進行適當的 HTML 編碼
    -   **備註**: 已完全移除所有不安全的 `MarkupString` 使用。在 `ChildCaseList.razor` 中將父母姓名顯示區塊改用安全的 Blazor 元件結構；在 `ChildCaseList.razor` 和 `CareCaseList.razor` 中修復 `GetStatusMessage` 方法，移除 `MarkupString` 並使用 CSS 類別控制樣式；在 `AdminList.razor` 中移除 `searchResultMessage` 的 `MarkupString` 包裝。

---

### **A04:2021 – Insecure Design (不安全的設計)**

-   **Problem #4.1:** [V] **【高風險】** **業務邏輯漏洞 - 狀態變更操作缺乏驗證**:
    -   **問題**: 狀態變更操作（如確認狀態、刪除操作）缺乏業務邏輯驗證和完整的權限檢查。
    -   **建議**: 實作完整的業務邏輯驗證和權限檢查機制
    -   **備註**: 已創建 `BusinessLogicValidationService.cs` 和 `BusinessRulesConfigService.cs` 提供統一的業務邏輯驗證。在 `CareCaseList.razor` 和 `ChildCaseList.razor` 中實作完整的狀態變更和刪除驗證，包括權限檢查、並發控制、業務規則約束、詳細稽核日誌等。所有敏感操作現在都經過嚴格的業務邏輯驗證。

---

### **A05:2021 – Security Misconfiguration (安全設定錯誤)**

-   **Problem #5.5:** [V] **【中風險】** **敏感資料暴露 (錯誤訊息)**:
    -   **問題**: 錯誤訊息向終端使用者揭露敏感系統資訊（例如堆疊追蹤、資料庫錯誤）。
    -   **建議**: 在生產環境中禁用詳細錯誤訊息，並提供通用的錯誤頁面。日誌應記錄詳細資訊，但不能暴露給使用者。
    -   **備註**: `Program.cs` 已配置在非開發環境中禁用詳細錯誤訊息，並導向到 `/Error` 頁面。需確保 `/Error` 頁面本身不顯示敏感資訊。

-   **Problem #5.6:** [V] **【中風險】** **缺少安全標頭**:
    -   **問題**: 應用程式在 HTTP 回應中未傳送適當的安全標頭（例如 `X-Content-Type-Options`, `X-Frame-Options`, `Content-Security-Policy`）。
    -   **建議**: 配置應用程式以發送推薦的安全 HTTP 標頭，以增強瀏覽器安全性。
    -   **備註**: 已在 `Program.cs` 中添加 `X-Content-Type-Options`, `X-Frame-Options`, `X-XSS-Protection`, `Referrer-Policy` 標頭。`Content-Security-Policy` (CSP) 需根據應用程式具體需求手動配置。

-   **Problem #5.7:** [V] **【中風險】** **`wwwroot/data/` 中的資訊洩露**:
    -   **問題**: `wwwroot/data/` 目錄中的敏感配置資料（例如 `EmailList.json`、`SMTP.json`）可直接透過 Web 存取。
    -   **建議**: 將這些敏感檔案移至非 Web 可存取的目錄，並透過後端服務提供受控存取。
    -   **備註**: 已將 `CaseBelong.json`、`ChildCareKind.json`、`EmailList.json` 和 `SMTP.json` 移動到 `App_Data/Config/` 目錄，並更新了 `Base/EmailService.cs` 和 `Base/JsonDataLoader.cs` 以從新位置讀取檔案。

-   **Problem #5.8:** [V] **【中風險】** **缺少 HTTPS 強制執行**:
    -   **問題**: 即使配置了 HTTPS，應用程式仍可透過 HTTP 存取，可能導致敏感資料在傳輸過程中被竊聽。
    -   **建議**: 配置 HTTP 嚴格傳輸安全 (HSTS) 和/或在 `Program.cs` 中強制執行 HTTPS 重定向。
    -   **備註**: `app.UseHsts();` 和 `app.UseHttpsRedirection();` 已在 `Program.cs` 中啟用，確保了 HTTPS 的強制執行。

-   **Problem #4.5:** [V] **【低風險】** **僅限客戶端驗證**:
    -   **問題**: 應用程式可能過度依賴客戶端驗證，而缺乏足夠的伺服器端驗證。
    -   **建議**: 始終在伺服器端重新驗證所有來自客戶端的輸入，因為客戶端驗證可以被繞過。
    -   **備註**: 已在多個關鍵服務和頁面（如 `ComRemitService.cs`、`ProjectProgressPresentationService.cs`、`CareCaseNewEdit.razor` 和 `ChildCaseNewEdit.razor`）中實作了伺服器端驗證。此為持續性的最佳實踐，應確保所有使用者輸入都在伺服器端進行驗證。

---

### **其他潛在問題 (非 OWASP Top 10 直接分類)**

-   **Problem #X.1:** [V] **【低風險】** **建置產物管理不當**:
    -   **問題**: `bin/` 和 `obj/` 目錄包含編譯後的二進位檔和中間建置檔案，其中包含調試符號 (.pdb)。
    -   **建議**: 確保這些目錄被版本控制系統忽略，並在生產部署中移除調試符號，以減少套件大小和潛在的資訊洩露。
    -   **備註**: 已創建 `.gitignore` 檔案以忽略 `bin/`、`obj/` 和其他敏感目錄。請手動檢查發布設定，確保在生產部署中不包含調試符號 (.pdb)。

-   **Problem #X.2:** [V] **【低風險】** **程式碼品質和可維護性**:
    -   **問題**: 程式碼庫的複雜性和大量依賴項可能導致難以維護和引入錯誤。
    -   **建議**: 實作程式碼審查、單元測試和自動化測試，並考慮重構以提高模組化和可讀性。
    -   **備註**: 這是一個持續性的改進工作。建議實施程式碼格式化工具、定期移除死程式碼、簡化複雜邏輯、添加清晰註釋、編寫單元測試以及優化依賴項管理。

---

### **A08:2021 – Software and Data Integrity Failures (軟體與資料完整性故障)**

-   **Problem #8.4:** [V] **【高風險】** **無限制檔案上傳漏洞**:
    -   **問題**: 在 `wwwroot/uploads/Reports/` 等目錄中，可能存在未經適當驗證檔案類型或內容的無限制檔案上傳，允許攻擊者上傳惡意檔案。
    -   **建議**: 實作嚴格的檔案上傳驗證，包括白名單檔案類型、限制檔案大小、掃描惡意內容，並將上傳檔案儲存在非 Web 可存取的目錄中。
    -   **備註**: 已創建完整的安全檔案上傳系統，包括 `SecureFileUploadService.cs`、`FileUploadConfigService.cs`、`SecureFileDownloadService.cs` 和 `FileMigrationService.cs`。實作了嚴格的檔案驗證（檔案簽名、內容掃描、白名單檔案類型）、安全儲存（非 Web 可存取的 `App_Data/SecureUploads/` 目錄）、權限控制下載、檔案遷移機制，以及管理介面 `FileSecurityManagement.razor`。所有檔案操作現在都經過完整的安全驗證和稽核記錄。

-   **Problem #3.2:** [V] **【高風險】** **跨站腳本漏洞 (Cross-Site Scripting - XSS)**:
    -   **問題**: 使用者提供的輸入未經適當編碼就直接渲染到 `Components/Pages/ComRemit/Index.razor` 和 `Components/Pages/ProjectProgress/Details.razor` 等 HTML 頁面，導致 XSS 漏洞。
    -   **建議**: 對所有使用者輸入進行輸出編碼，特別是在將其顯示在網頁上之前。使用 Blazor 內建的防 XSS 機制。
    -   **備註**: 已檢查 `Components/Pages/ComRemit/RemitedList.razor`、`Components/Pages/ProjectProgress/ProjectProgressEdit.razor` 和 `Components/Pages/ComRemit/RemitMt.razor`。由於 Blazor 的 `InputText` 和 `InputTextArea` 元件預設會對綁定到其 `Value` 屬性的字串進行 HTML 編碼，且未發現直接使用 `MarkupString` 或不安全 JavaScript 渲染使用者輸入的情況，因此這些頁面直接導致 XSS 漏洞的可能性較低。

-   **Problem #3.3:** [V] **【中風險】** **電子郵件標頭注入**:
    -   **問題**: `Base/EmailService.cs` 在構建電子郵件時，如果未對使用者提供的輸入進行適當驗證和清理，可能導致電子郵件標頭注入。
    -   **建議**: 在將使用者輸入用於電子郵件標頭（如收件人、主題）之前，進行嚴格的驗證和清理。
    -   **備註**: 已在 `Base/EmailService.cs` 的 `SendEmailAsync` 方法中對 `subject` 參數進行了換行符清理，以防止標頭注入。

---

### **A04:2021 – Insecure Design (不安全的設計)**

-   **Problem #6:** [V] **審查核心業務邏輯（如匯款功能），確保沒有設計層面的漏洞。**
-   **Problem #6.1:** [V] **【高風險】** **在 `UpdateRemitItemAsync` 函式中加入權限檢查**:
    -   **問題**: 此函式完全沒有權限檢查，任何登入的使用者都可以修改任一筆未彙整資料的金額與備註。
    -   **建議**: 應限制只有資料建立者或管理員可以修改。

-   **Problem #6.2:** [V] **【高風險】** **在 `Complete...` 系列函式中加入權限檢查**:
    -   **問題**: `CompleteRemitAsync`, `CompleteSelectedRemitAsync`, `CompleteSelectedRemitWithClassificationAsync` 等函式沒有權限檢查，任何使用者都能將資料標記為「已完成彙整」。
    -   **建議**: 應限制只有特定角色（如出納、會計）或管理員可以執行此操作。

-   **Problem #6.3:** [V] **【高風險】** **增加對匯款金額的伺服器端驗證**:
    -   **問題**: 程式碼未驗證 `RemitPrice`，可能允許輸入 0 或負數的金額。
    -   **建議**: 在 `RemitedList` 模型或服務層加入驗證規則，確保金額必須為正數。

-   **Problem #6.4:** [V] **【中風險】** **強化 `DeleteRemitItemAsync` 的權限邏輯**:
    -   **問題**: 目前僅限制非管理員不得刪除「已彙整」的資料，但任何使用者都可以刪除「未彙整」的資料。
    -   **建議**: 確認業務邏輯，應考慮限制只有資料建立者或管理員才能刪除未彙整的資料。

---

### **A05:2021 – Security Misconfiguration (安全設定錯誤)**

-   **Problem #5:** [V] **【低風險】** **評估並可能縮短 Session 的閒置逾時時間**:
    -   **問題**: Session 閒置逾時設定為 120 分鐘，對於處理敏感資料的應用程式來說可能過長。
    -   **建議**: 根據應用程式的實際安全需求，評估是否需要縮短此時間（例如 15-30 分鐘）。

-   **Problem #5.1:** [x] **【高風險】** **將敏感配置檔案移出 Web 可存取目錄**:
    -   **問題**: `wwwroot/restrictData/sysconfig.json` 檔案包含管理員帳號等敏感資訊，且直接暴露在 Web 可存取目錄中，可能導致資訊洩露。
    -   **建議**: 將 `sysconfig.json` 移至專案根目錄下非 Web 可存取的資料夾 (例如 `Config/sysconfig.json`)，並修改程式碼以從新位置讀取。

---

### **A06:2021 – Vulnerable and Outdated Components (有漏洞和過時的組件)**

-   **Problem #7:** [x] **驗證外部 DLL (`OAKsIlan*`) 的來源與完整性，並對專案進行軟體組成分析 (SCA) 以找出已知的套件漏洞。** **【低風險 - 目前可接受】**

---

### **A08:2021 – Software and Data Integrity Fail failures (軟體與資料完整性故障)**

-   **Problem #8.1:** [x] **【中風險】** **強化匯出檔案名稱的隨機性**:
        -   **問題**: 匯出檔案名稱僅包含時間戳，雖然降低了猜測難度，但仍有被猜測的潛在風險。
        -   **建議**: 在檔案名稱中加入 GUID 或更長的隨機字串，使其更難以被預測。
-   **Problem #8.2:** [x] **【中風險】** **考慮實作伺服器端下載 API**: (已透過 Minimal API 實作檔案串流下載，並將檔案儲存至非 Web 可存取目錄)
-   **Problem #8.3:** [x] **【低風險 - 目前可接受】** **為匯出功能實施頻率限制 (Rate Limiting)**:
        -   **問題**: 頻繁觸發匯出功能可能導致伺服器資源耗盡，造成拒絕服務 (DoS)。
        -   **建議**: 限制每個使用者在一定時間內可以匯出的次數。
        -   **備註**: 此為較複雜的修改，目前列為可接受風險。

-   **Problem #8.5:** [V] **【高風險】** **JSON 反序列化漏洞**:
    -   **問題**: 如果 `Base/JsonDataLoader.cs` 在沒有適當類型檢查或驗證的情況下反序列化不受信任的 JSON，則可能存在反序列化漏洞風險。
    -   **建議**: 在反序列化不受信任的資料時，使用安全的反序列化方法，並實作類型約束和驗證。
    -   **備註**: 已在 `Base/JsonDataLoader.cs` 中所有 JSON 反序列化的方法中添加了 `JsonSerializerOptions`，並設定了 `MaxDepth` 以防止深度巢狀攻擊。

---

### **A09:2021 – Security Logging and Monitoring Failures (安全日誌記錄與監控失敗)**

-   **Problem #6.5:** [V] **【中風險】** **為所有寫入操作增加稽核日誌**:
    -   **問題**: 所有重要操作（新增、修改、刪除、完成彙整）都沒有留下稽核紀錄。
    -   **建議**: 在操作成功後，記錄執行人員、時間、IP 位址、操作類型與相關資料 ID。

---

### **需要進一步分析的項目**

-   **Problem #6:** [V] **A04:2021 – Insecure Design (審查核心業務邏輯（如匯款功能），確保沒有設計層面的漏洞。)**
-   **Problem #7:** [x] **A08:2021 – Software and Data Integrity Failures (軟體與資料完整性故障)**: 驗證外部 DLL (`OAKsIlan*`) 的來源與完整性，並對專案進行軟體組成分析 (SCA) 以找出已知的套件漏洞。 **【低風險 - 目前可接受】**

---

### **匯款功能 (ComRemitService) 設計審查發現**

-   **Problem #6.1:** [V] **【高風險】** **在 `UpdateRemitItemAsync` 函式中加入權限檢查**:
    -   **問題**: 此函式完全沒有權限檢查，任何登入的使用者都可以修改任一筆未彙整資料的金額與備註。
    -   **建議**: 應限制只有資料建立者或管理員可以修改。

-   **Problem #6.2:** [V] **【高風險】** **在 `Complete...` 系列函式中加入權限檢查**:
    -   **問題**: `CompleteRemitAsync`, `CompleteSelectedRemitAsync`, `CompleteSelectedRemitWithClassificationAsync` 等函式沒有權限檢查，任何使用者都能將資料標記為「已完成彙整」。
    -   **建議**: 應限制只有特定角色（如出納、會計）或管理員可以執行此操作。

-   **Problem #6.3:** [V] **【高風險】** **增加對匯款金額的伺服器端驗證**:
    -   **問題**: 程式碼未驗證 `RemitPrice`，可能允許輸入 0 或負數的金額。
    -   **建議**: 在 `RemitedList` 模型或服務層加入驗證規則，確保金額必須為正數。

-   **Problem #6.4:** [V] **【中風險】** **強化 `DeleteRemitItemAsync` 的權限邏輯**:
    -   **問題**: 目前僅限制非管理員不得刪除「已彙整」的資料，但任何使用者都可以刪除「未彙整」的資料。
    -   **建議**: 確認業務邏輯，應考慮限制只有資料建立者或管理員才能刪除未彙整的資料。

-   **Problem #6.5:** [V] **【中風險】** **為所有寫入操作增加稽核日誌**:
    -   **問題**: 所有重要操作（新增、修改、刪除、完成彙整）都沒有留下稽核紀錄。
    -   **建議**: 在操作成功後，記錄執行人員、時間、IP 位址、操作類型與相關資料 ID。

-   **Problem #6.6:** [V] **【低風險】** **為所有金融交易操作啟用資料庫交易**:
    -   **問題**: `CompleteRemitAsync` 和 `CompleteSelectedRemitAsync` 未使用明確的資料庫交易（Transaction）。
    -   **建議**: 使用 `BeginTransactionAsync` 將這些操作包裝起來，確保資料一致性。

---

### **其他潛在問題 (非 OWASP Top 10 直接分類)**

-   **Problem #X.1:** [ ] **【低風險】** **建置產物管理不當**:
    -   **問題**: `bin/` 和 `obj/` 目錄包含編譯後的二進位檔和中間建置檔案，其中包含調試符號 (.pdb)。
    -   **建議**: 確保這些目錄被版本控制系統忽略，並在生產部署中移除調試符號，以減少套件大小和潛在的資訊洩露。

-   **Problem #X.2:** [ ] **【低風險】** **程式碼品質和可維護性**:
    -   **問題**: 程式碼庫的複雜性和大量依賴項可能導致難以維護和引入錯誤。
    -   **建議**: 實作程式碼審查、單元測試和自動化測試，並考慮重構以提高模組化和可讀性。
