@page "/ComRemit/RemitedList"
@rendermode @(new InteractiveServerRenderMode(prerender: false))
@using Intra2025.Models.ComRemit
@using Intra2025.Services
@using Intra2025.Components.Base
@using Microsoft.AspNetCore.Components.Web
@inject ComRemitedListService RemitedListService
@inject IJSRuntime JSRuntime
@inherits BasePageComponent

<PageTitle>已彙整清單</PageTitle>

<!-- 統一的樣式覆蓋 -->
<style>
    .remited-list-page {
        width: 100%;
        margin: 0;
        padding: 20px;
        box-sizing: border-box;
    }
</style>

<div class="remited-list-page">
    <div class="card">
        <div class="card-header">
            <h4 style="color: #0033CC; font-weight: bold;">【已彙整清單(Step3)】</h4>
        </div>
        <div class="card-body" style="padding: 20px;">
            <div class="row mb-3">
                <div class="col-md-4">
                    <div class="d-flex align-items-stretch gap-2 mb-3">
                        <input @bind="searchKeyword" @onkeypress="OnKeyPress" class="form-control" style="width: 300px;"
                               placeholder="搜尋系統編號/收款人戶名/用途說明" />
                        <button class="btn btn-primary d-flex align-items-center justify-content-center" style="height: 38px; min-width: 60px;" @onclick="SearchData">搜尋</button>
                        <button class="btn btn-secondary d-flex align-items-center justify-content-center" style="height: 38px; min-width: 60px;" @onclick="ResetSearch">重設</button>
                    </div>
                </div>
            </div>

@if (!string.IsNullOrEmpty(errorMessage))
{
    <div class="alert alert-danger">@errorMessage</div>
}

@if (isLoading)
{
    <div class="text-center">
        <div class="spinner-border" role="status">
            <span class="visually-hidden">載入中...</span>
        </div>
        <p>載入中...</p>
    </div>
}
else if (currentPageData != null)
{
    @if (currentPageData?.Any() == true)
    {
        <!-- 分頁控制項 - 放在表格上方 -->
        <div class="pagination-controls mb-3 d-flex justify-content-between align-items-center">
            <div>
                <span class="text-muted">總共 @filteredRemitedList.Count 筆，目前顯示第 @currentPage 頁 / 共 @totalPages 頁</span>
            </div>
            <div>
                <button class="btn btn-outline-secondary btn-sm" @onclick="GoToFirstPage" disabled="@(currentPage == 1)">第1頁</button>
                <button class="btn btn-outline-secondary btn-sm" @onclick="GoPreviousPage" disabled="@(currentPage == 1)">上1頁</button>
                <span class="mx-2">第 @currentPage 頁 / 共 @totalPages 頁</span>
                <button class="btn btn-outline-secondary btn-sm" @onclick="GoNextPage" disabled="@(currentPage == totalPages)">下1頁</button>
                <button class="btn btn-outline-secondary btn-sm" @onclick="GoToLastPage" disabled="@(currentPage == totalPages)">最後1頁</button>
            </div>
        </div>

        <table class="table table-hover" style="border: 1px solid #e9ecef; border-collapse: collapse;">
            <thead>
                <tr style="background-color: #f8f9fa;">
                    <th style="border: 1px solid #dee2e6; padding: 10px; font-weight: 600; color: #495057;">系統編號</th>
                    <th style="border: 1px solid #dee2e6; padding: 10px; font-weight: 600; color: #495057;width:135px;">筆數/申請金額</th>
                    <th style="border: 1px solid #dee2e6; padding: 10px; font-weight: 600; color: #495057;">前5筆明細</th>
                    <th style="border: 1px solid #dee2e6; padding: 10px; font-weight: 600; color: #495057;">用途說明/彙整時間</th>
                    <th style="border: 1px solid #dee2e6; padding: 10px; font-weight: 600; color: #495057;width:125px;align:center">操作</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var item in currentPageData)
                {
                    <tr>
                        <td style="border: 1px solid #e9ecef; padding: 12px; vertical-align: top;">@item.ConSno</td>
                        <td style="border: 1px solid #e9ecef; padding: 12px; vertical-align: top;">共 @item.Count 筆 <br />
                            @item.TotalAmount.ToString("N0") </td>
                        <td style="border: 1px solid #e9ecef; padding: 12px; vertical-align: top;">
                            @if (item.Details != null && item.Details.Any())
                            {
                                <table class="table table-sm" style="margin-bottom: 0;font-size:12px; border: 1px solid #c4f5d2;">
                                    <thead>
                                        <tr style="background-color: #f8f9fa;">
                                            <th
                                                style="border: 1px solid #c4f5d2; padding: 8px; font-size: 0.799rem; font-weight: 500;         background-color: rgb(51, 51, 51); width: 50px;">
                                                序號</th>
                                            <th
                                                style="border: 1px solid #c4f5d2; padding: 8px; font-size: 0.799rem; font-weight: 500;         background-color: rgb(51, 51, 51);">
                                                收款人戶名</th>
                                            <th
                                                style="border: 1px solid #c4f5d2; padding: 8px; font-size: 0.799rem; font-weight: 500;         background-color: rgb(51, 51, 51);; width: 80px;">
                                                行號</th>
                                            <th
                                                style="border: 1px solid #c4f5d2; padding: 8px; font-size: 0.799rem; font-weight: 500;         background-color: rgb(51, 51, 51);;">
                                                帳號</th>
                                            <th
                                                style="border: 1px solid #c4f5d2; padding: 8px; font-size: 0.799rem; font-weight: 500;         background-color: rgb(51, 51, 51);;">
                                                金額</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @{
                                            int rowIndex = 1;
                                        }
                                        @foreach (var d in item.Details)
                                        {
                                            <tr>
                                                <td style="border: 1px solid #c4f5d2; padding: 6px; font-size: 0.799rem; text-align: center;">@rowIndex</td>
                                                <td style="border: 1px solid #c4f5d2; padding: 6px; font-size: 0.799rem;">@d.CollecName</td>
                                                <td style="border: 1px solid #c4f5d2; padding: 6px; font-size: 0.799rem; text-align: center;">@d.CollectNo</td>
                                                <td style="border: 1px solid #c4f5d2; padding: 6px; font-size: 0.799rem;">@d.CollecAcc</td>
                                                <td style="border: 1px solid #c4f5d2; padding: 6px; font-size: 0.799rem;">@d.RemitPrice?.ToString("N0")</td>
                                            </tr>
                                            rowIndex++;
                                        }
                                    </tbody>
                                </table>
                            }
                            else
                            {
                                <span class="text-muted">無明細資料</span>
                            }
                        </td>
                        <td style="border: 1px solid #e9ecef; padding: 12px; vertical-align: top;">@item.ConMemo <br /> 【
                            @item.ConDate 】</td>
                        <td style="border: 1px solid #e9ecef; padding: 12px; vertical-align: top;">
                            <div class="d-flex gap-2 flex-wrap">
                                <button class="btn btn-info btn-sm" @onclick="() => Print(item.ConSno ?? 0)">列印</button>
                                <button class="btn btn-success btn-sm" @onclick="() => Export(item.ConSno ?? 0)">匯出</button>
                                <button class="btn btn-danger btn-sm" @onclick="() => Delete(item.ConSno ?? 0)">刪除</button>
                            </div>
                        </td>
                    </tr>
                }
            </tbody>
        </table>
    }

    <!-- 資料統計資訊 -->
    <div style="margin-top: 20px; color: #6c757d; font-size: 0.9rem;">
        <p>共 @allRemitedList.Count 筆資料 @(filteredRemitedList.Count != allRemitedList.Count ? $"，搜尋結果: {filteredRemitedList.Count} 筆" : "")</p>
    </div>
}
        </div>
    </div>
</div>

@code {
    // 分頁相關變數
    private List<RemitedListViewModel> allRemitedList = new List<RemitedListViewModel>();
    private List<RemitedListViewModel> filteredRemitedList = new List<RemitedListViewModel>();
    private List<RemitedListViewModel> currentPageData = new List<RemitedListViewModel>();
    
    private int currentPage = 1;
    private int pageSize = 10; // 每頁顯示10筆
    private int totalPages = 0;
    private bool isLoading = true;
    
    // 原有變數
    private string searchKeyword = "";
    private string errorMessage = "";

    protected override async Task OnInitializedAsync()
    {
        // SSO 驗證已在 BasePageComponent 中自動執行
        await base.OnInitializedAsync();
        await LoadData();
    }

    private async Task LoadData()
    {
        try
        {
            isLoading = true;
            errorMessage = "";
            StateHasChanged();

            // 檢查用戶是否已登入
            if (_userState == null || string.IsNullOrEmpty(_userState.Account))
            {
                errorMessage = "用戶未登入或帳號資訊不完整";
                Logger.LogError("用戶未登入，停止載入RemitedList資料");
                return;
            }

            // 根據登入用戶帳號載入資料 (權限控制 - 使用 ConPer 欄位)
            Logger.LogInformation($"開始執行 GetRemitedListByUserAccountAsync，用戶帳號: {_userState.Account}");
            var result = await RemitedListService.GetRemitedListByUserAccountAsync(_userState.Account);
            
            allRemitedList = result ?? new List<RemitedListViewModel>();
            filteredRemitedList = allRemitedList.ToList();
            
            Logger.LogInformation($"GetRemitedListByUserAccountAsync 完成，共 {allRemitedList.Count} 筆資料");
            
            currentPage = 1;
            UpdatePagination();
        }
        catch (Exception ex)
        {
            errorMessage = $"載入資料時發生錯誤: {ex.Message}";
            Logger.LogError(ex, "載入RemitedList資料時發生錯誤");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task SearchData()
    {
        try
        {
            isLoading = true;
            errorMessage = "";
            StateHasChanged();

            // 檢查用戶是否已登入
            if (_userState == null || string.IsNullOrEmpty(_userState.Account))
            {
                errorMessage = "用戶未登入或帳號資訊不完整";
                Logger.LogError("用戶未登入，停止搜尋RemitedList資料");
                return;
            }

            if (string.IsNullOrWhiteSpace(searchKeyword))
            {
                // 如果搜尋關鍵字為空，顯示所有資料
                filteredRemitedList = allRemitedList.ToList();
            }
            else
            {
                // 根據登入用戶帳號搜尋資料 (權限控制 - 使用 ConPer 欄位)
                Logger.LogInformation($"開始執行 SearchRemitedListByUserAccountAsync，用戶帳號: {_userState.Account}，關鍵字: {searchKeyword}");
                var result = await RemitedListService.SearchRemitedListByUserAccountAsync(searchKeyword, _userState.Account);
                filteredRemitedList = result ?? new List<RemitedListViewModel>();
                Logger.LogInformation($"SearchRemitedListByUserAccountAsync 完成，共 {filteredRemitedList.Count} 筆資料");
            }

            currentPage = 1;
            UpdatePagination();
        }
        catch (Exception ex)
        {
            errorMessage = $"搜尋時發生錯誤: {ex.Message}";
            Logger.LogError(ex, "搜尋RemitedList時發生錯誤");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task ResetSearch()
    {
        searchKeyword = "";
        filteredRemitedList = allRemitedList.ToList();
        currentPage = 1;
        UpdatePagination();
        StateHasChanged();
        await Task.CompletedTask;
    }

    private async Task OnKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            await SearchData();
        }
    }

    // 分頁控制方法
    private void UpdatePagination()
    {
        try
        {
            totalPages = (int)Math.Ceiling((double)filteredRemitedList.Count / pageSize);
            int skip = (currentPage - 1) * pageSize;
            currentPageData = filteredRemitedList.Skip(skip).Take(pageSize).ToList();
            Logger.LogInformation($"UpdatePagination: 總資料數={filteredRemitedList.Count}, 總頁數={totalPages}, 目前頁={currentPage}, 當前頁資料數={currentPageData.Count}");
        }
        catch (Exception ex)
        {
            errorMessage = $"更新分頁時發生錯誤: {ex.Message}";
            Logger.LogError(ex, "UpdatePagination 錯誤: {Message}", ex.Message);
        }
    }

    private void GoToPage(int page)
    {
        try
        {
            if (page >= 1 && page <= totalPages)
            {
                currentPage = page;
                UpdatePagination();
                StateHasChanged();
                Logger.LogInformation($"跳轉到第 {page} 頁，共 {totalPages} 頁，當前頁資料數: {currentPageData.Count}");
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"換頁時發生錯誤: {ex.Message}";
            StateHasChanged();
        }
    }

    private async Task GoToFirstPage()
    {
        GoToPage(1);
        await Task.CompletedTask;
    }

    private async Task GoPreviousPage()
    {
        if (currentPage > 1)
        {
            GoToPage(currentPage - 1);
        }
        await Task.CompletedTask;
    }

    private async Task GoNextPage()
    {
        if (currentPage < totalPages)
        {
            GoToPage(currentPage + 1);
        }
        await Task.CompletedTask;
    }

    private async Task GoToLastPage()
    {
        GoToPage(totalPages);
        await Task.CompletedTask;
    }

    // 原有功能方法
    private async Task Print(int conSno)
    {
        try
        {
            var result = await RemitedListService.PrintPdfAsync(conSno);
            
            if (result.PdfBytes != null && result.PdfBytes.Length > 0)
            {
                // 將 PDF 轉換為 Base64
                var base64 = Convert.ToBase64String(result.PdfBytes);
                
                // 驗證 Base64 內容是否為有效的 PDF（檢查前幾個字元）
                var pdfHeader = result.PdfBytes.Take(4).ToArray();
                var isValidPdf = pdfHeader.Length >= 4 && pdfHeader[0] == 0x25 && pdfHeader[1] == 0x50 && pdfHeader[2] == 0x44 && pdfHeader[3] == 0x46;
                
                if (!isValidPdf)
                {
                    await JSRuntime.InvokeVoidAsync("alert", "PDF 檔案格式無效，請聯絡系統管理員");
                    return;
                }
                
                // 嘗試多種方式顯示 PDF
                try
                {
                    // 方法1：使用 downloadFile 直接下載
                    await JSRuntime.InvokeVoidAsync("downloadFile", result.Filename, base64);
                }
                catch (Exception)
                {
                    // 方法2：在新視窗中開啟（備用方案）
                    var dataUrl = $"data:application/pdf;base64,{base64}";
                    await JSRuntime.InvokeVoidAsync("open", dataUrl, "_blank");
                }
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert", "無法產生PDF檔案 - 檔案為空或生成失敗");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"列印失敗: {ex.Message}");
        }
    }

    private async Task Export(int conSno)
    {
        try
        {
            var (content, filename) = await RemitedListService.ExportTxtAsync(conSno);
            if (!string.IsNullOrEmpty(content) && !string.IsNullOrEmpty(filename))
            {
                // 使用 Big5 編碼，如原始程式碼所示
                var bytes = System.Text.Encoding.GetEncoding("big5").GetBytes(content);
                var base64 = Convert.ToBase64String(bytes);
                await JSRuntime.InvokeVoidAsync("downloadFile", filename, base64);
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert", "無法產生匯出檔案");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"匯出失敗: {ex.Message}");
            Logger.LogError(ex, "匯出CSV檔時發生錯誤");
        }
    }

    private async Task Copy(int conSno)
    {
        try
        {
            // 複製功能 - 這裡可以實作複製到剪貼簿或其他邏輯
            await JSRuntime.InvokeVoidAsync("alert", $"複製功能尚未實作 (ConSno: {conSno})");
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"複製失敗: {ex.Message}");
        }
    }

    private async Task Delete(int conSno)
    {
        try
        {
            var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", "確定要刪除這筆資料嗎？");
            if (confirmed)
            {
                var success = await RemitedListService.DeleteRemitedAsync(conSno);
                if (success)
                {
                    await JSRuntime.InvokeVoidAsync("alert", "刪除成功！");
                    await LoadData(); // 重新載入資料
                }
                else
                {
                    await JSRuntime.InvokeVoidAsync("alert", "刪除失敗！");
                }
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"刪除失敗: {ex.Message}");
            Logger.LogError(ex, "刪除RemitedList時發生錯誤");
        }
    }
}
