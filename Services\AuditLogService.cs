using Intra2025.Components.Base;
using Microsoft.Extensions.Logging;

namespace Intra2025.Services
{
    public class AuditLogService
    {
        private readonly ILogger<AuditLogService> _logger;
        private readonly UserState _userState;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public AuditLogService(ILogger<AuditLogService> logger, UserState userState, IHttpContextAccessor httpContextAccessor)
        {
            _logger = logger;
            _userState = userState;
            _httpContextAccessor = httpContextAccessor;
        }

        public void Log(string action, string details)
        {
            var ipAddress = _httpContextAccessor.HttpContext?.Connection?.RemoteIpAddress?.ToString() ?? "N/A";
            _logger.LogInformation("Audit Log: User={User}, Action={Action}, IP={IpAddress}, Details={Details}", 
                _userState.Account, action, ipAddress, details);
        }
    }
}
